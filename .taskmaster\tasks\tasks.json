{"master": {"tasks": [{"id": 1, "title": "Project Initialization and VCS Setup", "description": "Initialize a new Astro.js project with TypeScript. Set up a new private GitHub repository and push the initial project structure. Configure the repository for Vercel integration.", "details": "Use the command `npm create astro@latest -- --template minimal --typescript strict` to create a new project. Initialize a git repository, create a `.gitignore` file, and make the initial commit. Create a new private repository on GitHub and push the local repository. This forms the foundation for all subsequent development and deployment.", "testStrategy": "Verify that the project is created successfully and runs locally using `npm run dev`. Confirm the repository is correctly set up on GitHub and that the initial commit is present.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Initialize Astro.js Project", "description": "Create a new Astro.js project locally using the specified minimal template with strict TypeScript settings.", "dependencies": [], "details": "Open your terminal and run the command `npm create astro@latest -- --template minimal --typescript strict`. Follow the prompts to name your project directory. Once complete, navigate into the new project directory and run `npm install` to install all necessary dependencies.", "status": "pending", "testStrategy": "After installation, run `npm run dev`. The development server should start successfully, and you should be able to view the default Astro page in your browser at the specified local address (e.g., http://localhost:4321)."}, {"id": 2, "title": "Initialize Local Git Repository", "description": "Initialize a new Git repository in the project folder and create a `.gitignore` file to exclude build artifacts and environment variables.", "dependencies": [1], "details": "In the root of the project directory, run `git init`. Create a new file named `.gitignore` and add the following entries to it: `node_modules/`, `dist/`, `.astro/`, `.env`, `*.log`.", "status": "pending", "testStrategy": "Run `git status`. The command should list the project files as untracked but should not show any of the directories or files specified in the `.gitignore` file (e.g., `node_modules`)."}, {"id": 3, "title": "Create Initial Git Commit", "description": "Stage all the initial project files and create the first commit to establish a baseline for the project's version history.", "dependencies": [2], "details": "Run `git add .` to stage all the newly created project files. Then, execute `git commit -m \"Initial commit: Setup Astro.js minimal project with TypeScript\"` to save the changes to the local repository.", "status": "pending", "testStrategy": "Run `git log --oneline`. The output should display a single commit with the commit message specified in the details."}, {"id": 4, "title": "Create and Link Private GitHub Repository", "description": "Create a new private repository on GitHub and push the local repository's initial commit to it.", "dependencies": [3], "details": "Go to GitHub and create a new private repository (do not initialize it with a README or license). Copy the repository's URL. In your local terminal, run `git remote add origin <YOUR_GITHUB_REPO_URL>`. Then, rename the default branch to main with `git branch -M main`. Finally, push the initial commit using `git push -u origin main`.", "status": "pending", "testStrategy": "Refresh the repository page on GitHub. The project files from your local machine should now be visible in the remote repository's main branch."}, {"id": 5, "title": "Configure Vercel Integration", "description": "Connect the new GitHub repository to a Vercel project to set up automatic deployments.", "dependencies": [4], "details": "Log in to your Vercel account. Select 'Add New... > Project'. Choose the newly created GitHub repository. Vercel should auto-detect the Astro framework. Verify the build settings (Framework Preset: Astro, Output Directory: dist) and click 'Deploy'.", "status": "pending", "testStrategy": "Once the deployment is complete, visit the Vercel-provided URL. The live site should display the default Astro project page. Make a small change locally, commit, and push to GitHub to verify that a new deployment is automatically triggered on Vercel."}]}, {"id": 2, "title": "Integrate and Configure Tailwind CSS", "description": "Integrate Tailwind CSS into the Astro project. Configure the `tailwind.config.mjs` file to include the brand's custom color palette and typography as specified in the PRD.", "details": "Run `npx astro add tailwind` to add Tailwind CSS to the project. In `tailwind.config.mjs`, extend the theme to include the specified colors (primary: #3a86ff, secondary: #0a2463, accent: #ff9e00) and fonts (Montserrat for headings, Poppins for body, Fira Code for code). Set up base styles in `src/styles/global.css`.", "testStrategy": "Create a test page and apply custom color and font classes. Verify that the styles are applied correctly in the browser. Check that the Tailwind CSS utility classes are working as expected.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Install and Initialize Tailwind CSS", "description": "Add the official Astro Tailwind CSS integration to the project. This command will install necessary dependencies and generate the required configuration files.", "dependencies": [], "details": "In the project's root directory, execute the command `npx astro add tailwind`. Follow the interactive prompts to confirm the installation. This will create `tailwind.config.mjs` and `postcss.config.cjs`, and update `astro.config.mjs`.", "status": "pending", "testStrategy": "After running the command, verify that `@astrojs/tailwind` and `tailwindcss` are listed as dependencies in `package.json`. Confirm the existence of `tailwind.config.mjs` and that the Tailwind integration is present in the `integrations` array within `astro.config.mjs`."}, {"id": 2, "title": "Configure Custom Color Palette", "description": "Extend the Tailwind theme to include the brand's specific color palette for primary, secondary, and accent colors.", "dependencies": [1], "details": "Edit the `tailwind.config.mjs` file. Inside the `theme.extend` object, add a `colors` object with the following key-value pairs: `primary: '#3a86ff'`, `secondary: '#0a2463'`, and `accent: '#ff9e00'`.", "status": "pending", "testStrategy": "Create a test page or component. Apply utility classes like `bg-primary`, `text-secondary`, and `border-accent` to different elements. Open the page in a browser and use the developer tools to inspect the elements and confirm they are rendered with the correct hex color codes."}, {"id": 3, "title": "Configure Custom Typography", "description": "Extend the Tailwind theme to include the project's specified fonts for headings, body text, and code blocks.", "dependencies": [1], "details": "Edit the `tailwind.config.mjs` file. Inside `theme.extend`, add a `fontFamily` object. Define keys for the font families, for example: `heading: ['Montserrat', 'sans-serif']`, `sans: ['Poppins', 'sans-serif']`, and `mono: ['Fira Code', 'monospace']`. Ensure these fonts are imported in the main layout file, typically from a service like Google Fonts.", "status": "pending", "testStrategy": "Apply the custom font utility classes (e.g., `font-heading`, `font-sans`, `font-mono`) to text elements on a test page. Use the browser's developer tools to inspect the computed styles and verify that the correct `font-family` stack is being applied to each element."}, {"id": 4, "title": "Implement Base Global Styles", "description": "Set up foundational styles for common HTML elements in a global stylesheet using Tailwind's `@apply` directive.", "dependencies": [2, 3], "details": "In the `src/styles/global.css` file, ensure the three main Tailwind directives (`@tailwind base;`, `@tailwind components;`, `@tailwind utilities;`) are present. Within the base layer, define default styles. For example: `body { @apply font-sans text-secondary; }`, `h1, h2, h3 { @apply font-heading text-primary; }`, `code, pre { @apply font-mono; }`.", "status": "pending", "testStrategy": "Create a page with unstyled HTML elements like `<h1>`, `<p>`, and `<code>`. Verify that these elements automatically adopt the styles defined in `global.css` without requiring any utility classes to be added directly to them."}, {"id": 5, "title": "Verify Configuration and Production Build", "description": "Confirm that all custom configurations are working in the development environment and that unused CSS is correctly purged in the final production build.", "dependencies": [4], "details": "Run the development server (`npm run dev`) and check a page that uses a mix of standard and custom utility classes (e.g., `bg-primary`, `font-heading`, `md:p-8`). Then, run a production build (`npm run build`) and inspect the generated CSS file in the `dist/` directory to ensure it is minified and significantly smaller than the full Tailwind library.", "status": "pending", "testStrategy": "After building for production, serve the `dist` directory locally or check a preview deployment. Verify that all styles on the test page render correctly. The final CSS file size should be small, indicating that CSS purging was successful."}]}, {"id": 3, "title": "Develop Global Layout and Base Components", "description": "Create the main site layout including a header with navigation and a footer. This layout will be used across all pages to ensure a consistent structure and branding.", "details": "Create a `Layout.astro` component in `src/layouts/`. This component will include the HTML shell, head section with viewport and charset meta tags, and slots for page content. Develop `Header.astro` and `Footer.astro` components and import them into the main layout. The header should contain navigation links to Home, Portfolio, About, Resume, Resources, and Contact.", "testStrategy": "Create several empty pages using the main layout. Verify that the header and footer are displayed correctly on all pages and that navigation links work. Ensure the layout is responsive.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": [{"id": 1, "title": "Create Layout.astro Shell", "description": "Set up the basic `Layout.astro` component with the fundamental HTML structure, including `<html>`, `<head>`, and `<body>` tags, as well as meta tags and a content slot.", "dependencies": [], "details": "In the `src/layouts/` directory, create a new file named `Layout.astro`. This file should contain the standard HTML5 boilerplate (`<!DOCTYPE html>`, `<html>`, `<head>`, `<body>`). Inside the `<head>`, add `<meta charset=\"UTF-8\">` and `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">`. Place a `<slot />` tag inside the `<body>` to serve as a placeholder for page-specific content.", "status": "pending", "testStrategy": "Create a temporary page in `src/pages/` that uses this layout. Open the page in a browser and use 'View Page Source' to confirm the HTML structure, meta tags, and any content passed into the slot are rendered correctly."}, {"id": 2, "title": "Develop Header.astro Component", "description": "Create a reusable `Header.astro` component that contains the primary site navigation links.", "dependencies": [], "details": "In a new `src/components/` directory, create `Header.astro`. Inside this file, add a `<header>` element containing a `<nav>` element. Within the `<nav>`, create anchor (`<a>`) tags for the following links: Home (`/`), Portfolio (`/portfolio`), About (`/about`), Resume (`/resume`), Resources (`/resources`), and Contact (`/contact`).", "status": "pending", "testStrategy": "Create a test page that imports and renders only the `Header.astro` component. Verify that all specified navigation links are present, correctly labeled, and point to the correct URLs."}, {"id": 3, "title": "Develop Footer.astro Component", "description": "Create a reusable `Footer.astro` component to be displayed at the bottom of every page.", "dependencies": [], "details": "In the `src/components/` directory, create `Footer.astro`. Inside this file, add a `<footer>` element. For now, populate it with simple placeholder content, such as a copyright notice using JavaScript to dynamically display the current year: `© ${new Date().getFullYear()} Your Name`.", "status": "pending", "testStrategy": "Create a test page that imports and renders only the `Footer.astro` component. Verify that the footer element and its placeholder content are displayed correctly."}, {"id": 4, "title": "Integrate <PERSON><PERSON> and <PERSON><PERSON> into Layout.astro", "description": "Import and place the newly created `Header.astro` and `Footer.astro` components into the main `Layout.astro` file to form the complete page structure.", "dependencies": [1, 2, 3], "details": "Modify `src/layouts/Layout.astro`. Import the `Header` component from `../components/Header.astro` and the `Footer` component from `../components/Footer.astro`. Place the `<Header />` component within the `<body>`, before the `<slot />`. Place the `<Footer />` component within the `<body>`, after the `<slot />`.", "status": "pending", "testStrategy": "Revisit the test page from subtask 1 that uses `Layout.astro`. Confirm that the header now appears at the top of the page and the footer appears at the bottom, framing the page's main content."}, {"id": 5, "title": "Apply Basic Global Styles for Layout Structure", "description": "Add minimal global CSS to ensure the layout components (header, content, footer) are arranged correctly and provide a consistent base for future styling.", "dependencies": [4], "details": "Create a global stylesheet at `src/styles/global.css`. In this file, reset default margins on the body (`body { margin: 0; }`). To ensure the footer stays at the bottom of the page, apply a flexbox layout to the body: `body { display: flex; flex-direction: column; min-height: 100vh; }`. Wrap the `<slot />` in `Layout.astro` with a `<main>` tag and give it a style of `flex-grow: 1;`. Import the stylesheet into `Layout.astro` using a `<link>` tag in the `<head>`.", "status": "pending", "testStrategy": "View any page using the layout. The page should fill the full height of the browser viewport, with the footer positioned at the very bottom, even if the content is short. There should be no default browser margin around the edges of the page."}]}, {"id": 4, "title": "Implement Professional Homepage", "description": "Develop the homepage, including the hero section with a professional headshot, headline, value proposition, key skills, and clear call-to-action (CTA) buttons.", "details": "Build the `src/pages/index.astro` page. Implement the hero section using Tailwind CSS for styling. Use Astro's `<Image>` component for the headshot to ensure optimization (WebP/AVIF format). The CTAs should link to the portfolio and contact sections. The brief introduction should be included below the hero section.", "testStrategy": "Verify that the homepage matches the design requirements. Check that all text and images are displayed correctly. Test the CTA buttons to ensure they navigate to the correct sections/pages. Run an initial Lighthouse check for performance.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "Create `index.astro` and Set Up Basic Page Layout", "description": "Generate the `src/pages/index.astro` file and establish the fundamental HTML structure. This includes setting up the `head` with a title and importing a base layout component for consistent page structure.", "dependencies": [], "details": "Create the file `src/pages/index.astro`. Import and use a shared `Layout.astro` component to wrap the page content. Set the page title within the layout component's props. The initial body will be a placeholder for the hero section.", "status": "pending", "testStrategy": "Run the development server and navigate to the root URL. Verify that the page loads without errors and the browser tab displays the correct page title."}, {"id": 2, "title": "Build Hero Section Structure with Tailwind CSS", "description": "Implement the main structural layout for the hero section using Tailwind CSS. This involves creating responsive containers for the text content and the headshot image, likely using a flexbox or grid system.", "dependencies": [1], "details": "Within `index.astro`, create a `<section>` for the hero. Use `div` elements with Tailwind CSS classes (e.g., `flex`, `flex-col`, `md:flex-row`, `items-center`, `gap-8`) to define the layout for the text block and the image placeholder.", "status": "pending", "testStrategy": "Inspect the page in a browser. Use developer tools to toggle between mobile and desktop views to ensure the layout containers adapt correctly and are positioned as expected."}, {"id": 3, "title": "Add and Style Hero Text Content", "description": "Populate the hero section with the headline, value proposition, and a list of key skills. Apply Tailwind CSS utility classes for typography, spacing, and color.", "dependencies": [2], "details": "Add `<h1>` for the main headline, `<p>` for the value proposition, and a `<ul>` with `<li>` elements for the key skills. Use Tailwind classes like `text-4xl`, `font-bold`, `text-slate-600`, and `mt-4` to style the text.", "status": "pending", "testStrategy": "Visually review the page to confirm that all text content is present, legible, and styled correctly according to the design specifications for font size, weight, and color."}, {"id": 4, "title": "Integrate and Optimize Headshot Image", "description": "Place the professional headshot into the designated area of the hero section using Astro's `<Image>` component for automatic optimization to WebP/AVIF formats.", "dependencies": [2], "details": "Save the headshot image in the `src/assets/` directory. Use the `<Image>` component in `index.astro`, importing the image source directly. Apply Tailwind classes for sizing and styling, such as `w-48`, `h-48`, and `rounded-full`.", "status": "pending", "testStrategy": "Verify the image renders correctly on the page. Use browser developer tools to inspect the network tab and confirm the image is being served in an optimized format (e.g., .webp) and has the appropriate `srcset` attribute."}, {"id": 5, "title": "Implement CTA Buttons and Brief Introduction", "description": "Add two Call-to-Action (CTA) buttons linking to the portfolio and contact sections, and create the brief introduction section directly below the hero.", "dependencies": [3, 4], "details": "Create two `<a>` tags styled as buttons using Tailwind CSS (e.g., `bg-primary`, `text-white`, `py-2`, `px-4`, `rounded`). Set their `href` attributes to the target pages (e.g., `/portfolio/`, `/contact/`). Add a new `<section>` below the hero with a `<p>` tag containing the brief introduction.", "status": "pending", "testStrategy": "Click on each CTA button to ensure they navigate to the correct pages. Verify the introduction text is displayed below the hero section with appropriate spacing."}]}, {"id": 5, "title": "Setup Astro Content Collections for MDX", "description": "Configure Astro's Content Collections to manage portfolio case studies and resource links using MDX files. Define the schema for frontmatter in `src/content/config.ts`.", "details": "In `src/content/config.ts`, define two collections: `portfolio` and `resources`. For `portfolio`, the schema should include fields like `title`, `publishDate`, `problem`, `solution`, `technologies`, `role`, `results`, `repoUrl`, `liveUrl`, and `heroImage`. For `resources`, the schema should include `title`, `url`, `description`, `category`, and `tags`.", "testStrategy": "Create sample MDX files for both a portfolio item and a resource with valid frontmatter. Attempt to query the content using `getCollection('portfolio')` and `getCollection('resources')` in a test page to ensure the schemas are correctly configured and the content is accessible.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Create Content Collection Directories", "description": "Create the necessary directory structure within `src/content/` to house the MDX files for the `portfolio` and `resources` collections.", "dependencies": [], "details": "In the project's `src/` directory, create a new directory named `content`. Inside `src/content/`, create two subdirectories: `portfolio` and `resources`. This structure is required by Astro for content collections to be discovered.", "status": "pending", "testStrategy": "Verify that the directories `src/content/portfolio` and `src/content/resources` exist in the project's file structure."}, {"id": 2, "title": "Create Content Configuration File", "description": "Create the `config.ts` file inside the `src/content/` directory. This file will be used to define the schemas for all content collections.", "dependencies": [1], "details": "In the `src/content/` directory created in the previous step, create a new file named `config.ts`. This file will contain the schema definitions and export the collections.", "status": "pending", "testStrategy": "Check for the existence of the file at the path `src/content/config.ts`."}, {"id": 3, "title": "Define <PERSON> for 'portfolio' Collection", "description": "In `src/content/config.ts`, import `defineCollection` and `z` from `astro:content` and define the schema for the `portfolio` collection.", "dependencies": [2], "details": "Edit `src/content/config.ts`. Import `defineCollection` and `z`. Create a `portfolioCollection` constant using `defineCollection`. The schema should use `z.object` to define the fields: `title` (string), `publishDate` (date), `problem` (string), `solution` (string), `technologies` (array of strings), `role` (string), `results` (string), `repoUrl` (URL, optional), `liveUrl` (URL, optional), and `heroImage` (string, path to image).", "status": "pending", "testStrategy": "Add a sample MDX file to `src/content/portfolio/` with frontmatter matching the schema. Run `astro dev` or `astro check` and ensure no type errors are reported for the `portfolio` collection."}, {"id": 4, "title": "Define Schema for 'resources' Collection", "description": "In `src/content/config.ts`, define the schema for the `resources` collection, specifying the types for its frontmatter fields.", "dependencies": [2], "details": "In the same `src/content/config.ts` file, create a `resourcesCollection` constant using `defineCollection`. The schema should use `z.object` to define the fields: `title` (string), `url` (URL), `description` (string), `category` (string), and `tags` (array of strings).", "status": "pending", "testStrategy": "Add a sample MDX file to `src/content/resources/` with frontmatter matching the schema. Run `astro dev` or `astro check` and ensure no type errors are reported for the `resources` collection."}, {"id": 5, "title": "Export Defined Collections from config.ts", "description": "Export the defined `portfolio` and `resources` collections from `src/content/config.ts` so Astro can recognize and use them.", "dependencies": [3, 4], "details": "At the end of `src/content/config.ts`, add an `export const collections` object. This object should map the collection names to their defined schemas, like so: `export const collections = { 'portfolio': portfolioCollection, 'resources': resourcesCollection };`.", "status": "pending", "testStrategy": "Run the `astro check` command. The command should complete without errors. Attempt to query the collections using `getCollection('portfolio')` in a test Astro page to confirm they are accessible and correctly typed."}]}, {"id": 6, "title": "Build Portfolio Showcase Page", "description": "Create the main portfolio page that displays all case studies in a card-based layout. Each card should link to the detailed case study page.", "details": "Create a page at `src/pages/portfolio/index.astro`. Fetch all entries from the 'portfolio' content collection using `getCollection`. Map over the entries to render a grid of project cards. Each card should display the project title, a brief summary, and a thumbnail image. Use Tailwind CSS for a responsive grid layout.", "testStrategy": "Verify that all portfolio items from the content collection are displayed on the page. Check that the layout is responsive and cards maintain a consistent aspect ratio. Ensure each card links to the correct, dynamically generated detail page URL.", "priority": "medium", "dependencies": [3, 5], "status": "pending", "subtasks": [{"id": 1, "title": "Create Portfolio Index Page File", "description": "Set up the basic file structure for the portfolio showcase page to establish the new route.", "dependencies": [], "details": "Create a new file at `src/pages/portfolio/index.astro`. Add a basic Astro component structure with a Layout component and a placeholder `<h1>Portfolio</h1>` to ensure the page route is working correctly before adding complex logic.", "status": "pending", "testStrategy": "Run the development server and navigate to the `/portfolio` URL in a browser. Verify that the page loads and displays the 'Portfolio' heading without errors."}, {"id": 2, "title": "Fetch Portfolio Content Collection Data", "description": "Implement the logic within the portfolio page to retrieve all project entries from the Astro content collection.", "dependencies": [1], "details": "In the frontmatter script of `src/pages/portfolio/index.astro`, import `getCollection` from `astro:content`. Use `await getCollection('portfolio')` to fetch all entries and store them in a variable named `projects`. Use `console.log()` to output the fetched data to the terminal for verification.", "status": "pending", "testStrategy": "Run the development server and check the terminal console where the server is running. Verify that an array of project objects is logged and that the data structure matches the 'portfolio' collection schema."}, {"id": 3, "title": "Develop a Reusable ProjectCard Component", "description": "Create a self-contained Astro component for displaying a single portfolio project card.", "dependencies": [], "details": "Create a new file at `src/components/ProjectCard.astro`. This component should accept props for `title`, `summary`, `thumbnail`, and `slug`. The component's structure should be an `<a>` tag wrapping an `<img>`, an `<h2>`, and a `<p>`. The `href` of the `<a>` tag should be constructed as `/portfolio/${props.slug}`.", "status": "pending", "testStrategy": "Temporarily import and render the `ProjectCard` component on the `index.astro` page with static, hard-coded props to verify it displays the title, summary, and image correctly and that the link is generated as expected."}, {"id": 4, "title": "Implement Responsive Grid and Render Cards", "description": "Use the fetched project data to dynamically render a responsive grid of ProjectCard components on the portfolio page.", "dependencies": [2, 3], "details": "In `src/pages/portfolio/index.astro`, import the `ProjectCard` component. Create a container `<div>` and apply Tailwind CSS grid classes (e.g., `grid`, `gap-8`, `grid-cols-1`, `md:grid-cols-2`, `lg:grid-cols-3`). Use the `.map()` method on the `projects` array to render a `<ProjectCard />` for each project, passing the appropriate data (e.g., `project.data.title`, `project.slug`) as props.", "status": "pending", "testStrategy": "Navigate to the `/portfolio` page. Verify that a card is rendered for each entry in the content collection. Resize the browser window to confirm the grid layout adjusts from 1 to 2 to 3 columns at the specified breakpoints. Hover over a card and check if the browser status bar shows the correct link."}, {"id": 5, "title": "Style and Refine Project Cards", "description": "Apply final styling to the ProjectCard component using Tailwind CSS for a polished and professional appearance.", "dependencies": [4], "details": "In `src/components/ProjectCard.astro`, use Tailwind CSS utility classes to style the card. Add properties like `bg-white`, `rounded-lg`, `shadow-md`, and `overflow-hidden`. Style the text for readability and hierarchy. Add a hover effect, such as `hover:shadow-xl` or `hover:scale-105`, to provide visual feedback to the user.", "status": "pending", "testStrategy": "Visually inspect the `/portfolio` page. Confirm that all cards have a consistent and appealing design, including padding, shadows, and rounded corners. Test the hover effect by moving the mouse over the cards. Ensure the styling is maintained across different screen sizes."}]}, {"id": 7, "title": "Develop Dynamic Portfolio Detail Pages", "description": "Create a dynamic page template to display the full content of each portfolio case study, including the problem-solution-results structure, technologies, and diagrams.", "details": "Create a dynamic route at `src/pages/portfolio/[slug].astro`. Use `getStaticPaths` to generate a page for each entry in the portfolio collection. The page will fetch the content for the specific slug and render the full MDX body, including the problem statement, solution, results, and other details from the frontmatter. Use Astro's `<Content />` component to render the MDX.", "testStrategy": "Navigate to a few different portfolio detail pages. Verify that all content from the corresponding MDX file (frontmatter and body) is rendered correctly. Check that images and code blocks are styled properly.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": [{"id": 1, "title": "Create Dynamic Route and Implement getStaticPaths", "description": "Set up the basic file structure for dynamic portfolio pages by creating the `[slug].astro` file and defining the static paths from the portfolio content collection.", "dependencies": [], "details": "Create the file at `src/pages/portfolio/[slug].astro`. Import the `getCollection` API from `astro:content`. Implement the `getStaticPaths` function to iterate over the 'portfolio' collection, returning an array of objects with `params` (containing the slug) for each portfolio item.", "status": "pending", "testStrategy": "Run `astro dev` and verify that navigating to a URL like `/portfolio/project-a` (assuming `project-a.mdx` exists) does not result in a 404 error. Check the terminal for any build errors related to path generation."}, {"id": 2, "title": "Fetch and Pass Portfolio Entry Data", "description": "Within `getStaticPaths`, pass the full portfolio entry data for the corresponding slug to the page component via props.", "dependencies": [1], "details": "Modify the `getStaticPaths` function. For each entry in the collection, include a `props` key in the returned object, with its value being the `entry` itself. Then, in the component's script section, retrieve the entry using `Astro.props`.", "status": "pending", "testStrategy": "Add a `console.log(Astro.props.entry)` in the script section of `[slug].astro`. Navigate to a portfolio page and check the dev server's terminal output to confirm the complete entry object (with slug, data, body, etc.) is being logged."}, {"id": 3, "title": "Render Core MDX Content with <Content />", "description": "Use Astro's `<Content />` component to render the main body of the MDX file, which contains the problem-solution-results narrative.", "dependencies": [2], "details": "In the `[slug].astro` script, get the `Content` component by calling `const { Content } = await entry.render();`. Then, place the `<Content />` component within the page's HTML template section to render the compiled MDX.", "status": "pending", "testStrategy": "Navigate to a portfolio detail page. Verify that the main text and any embedded components from the corresponding `.mdx` file's body are displayed correctly on the page."}, {"id": 4, "title": "Display Frontmatter Metadata", "description": "Render the structured data from the portfolio entry's frontmatter, such as the project title, summary, and technology stack.", "dependencies": [2], "details": "Access the frontmatter data via `const { data } = Astro.props.entry;`. In the HTML template, use this `data` object to display key information. For example, use `<h1>{data.title}</h1>` for the title and map over `data.technologies` to create a list of tech tags.", "status": "pending", "testStrategy": "Check a rendered portfolio page and confirm that the title, a summary paragraph, and a list of technologies from the `.mdx` file's frontmatter are all visible and correctly populated."}, {"id": 5, "title": "Style the Portfolio Detail Page Layout", "description": "Apply CSS to style the portfolio detail page for a professional and readable presentation, ensuring a clear visual hierarchy for all content.", "dependencies": [3, 4], "details": "Add a `<style>` block in `[slug].astro` or import a shared stylesheet. Define styles for headings, paragraphs, blockquotes, lists, and code blocks within the `<Content />` area. Create a distinct layout for the frontmatter metadata, potentially in a sidebar or header section. Ensure images and diagrams are responsive.", "status": "pending", "testStrategy": "Visually inspect the portfolio detail page on both desktop and mobile viewports. Confirm that the layout is responsive, text is legible, spacing is appropriate, and the overall design is consistent with the rest of the website."}]}, {"id": 8, "title": "Implement About Section", "description": "Develop the 'About' section, including the professional journey narrative, career highlights, technical expertise breakdown, and personal interests.", "details": "Create the `src/pages/about.astro` page. The content can be sourced from a single MDX file or directly within the Astro component. Structure the page using semantic HTML (e.g., `<h2>` for sections like 'My Journey', 'Technical Expertise'). The technical expertise section should be categorized as specified in the PRD.", "testStrategy": "Review the content on the About page to ensure it matches the PRD requirements. Check for proper heading structure and readability. Verify that the layout is clean and responsive.", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "Create Basic 'About' Page Structure", "description": "Set up the initial `src/pages/about.astro` file, including the basic page layout, a primary `<h1>` heading, and placeholder `<h2>` sections for the main content areas.", "dependencies": [], "details": "Create the file `src/pages/about.astro`. Import and apply the main site layout component. Add a main heading `<h1>About Me</h1>`. Create placeholder `<section>` elements with `<h2>` headings for 'My Journey', 'Technical Expertise', and 'Personal Interests' to structure the page content.", "status": "pending", "testStrategy": "Navigate to the `/about` URL in a development server. Verify that the page loads with the main layout, the 'About Me' heading is visible, and the placeholder section titles are present."}, {"id": 2, "title": "Add Professional Journey Narrative Content", "description": "Populate the 'My Journey' section with the professional narrative and career highlights, detailing work history and key achievements.", "dependencies": [1], "details": "Within the 'My Journey' section in `about.astro`, add the narrative content using semantic HTML. Use `<p>` tags for paragraphs and a `<ul>` with `<li>` elements for a list of career highlights or key accomplishments. The content can be written directly in the component or sourced from a separate MDX/Markdown file.", "status": "pending", "testStrategy": "Review the rendered `/about` page to ensure the professional journey text is displayed correctly, is well-formatted, and is free of typographical errors. Inspect the HTML to confirm the use of semantic tags."}, {"id": 3, "title": "Implement Categorized Technical Expertise Section", "description": "Develop the 'Technical Expertise' section, organizing skills into distinct categories such as 'Languages', 'Frameworks & Libraries', and 'Tools' as specified in the PRD.", "dependencies": [1], "details": "Under the `<h2>Technical Expertise</h2>` heading, create subsections for each skill category. Use `<h3>` for category titles. For each category, list the relevant skills using a `<ul>` and `<li>` structure for clarity and readability.", "status": "pending", "testStrategy": "Verify on the `/about` page that the technical skills are listed under their correct categories. Check that the visual hierarchy created by `<h2>` and `<h3>` tags is clear. Ensure all specified skills are included."}, {"id": 4, "title": "Incorporate Personal Interests Content", "description": "Add a section to the 'About' page that shares personal interests and hobbies to provide a more well-rounded and relatable profile.", "dependencies": [1], "details": "Create a new `<section>` with a heading like `<h2>Beyond the Code</h2>` or `<h2>Personal Interests</h2>`. Add a brief paragraph or a list describing hobbies and interests outside of professional work.", "status": "pending", "testStrategy": "Confirm that the personal interests section appears on the rendered page and is stylistically consistent with the other content sections. Read through the content to check for clarity and tone."}, {"id": 5, "title": "Apply Styling and Ensure Responsiveness", "description": "Apply CSS styling to all elements on the 'About' page for a polished and professional appearance. Ensure the layout is fully responsive and accessible across different screen sizes.", "dependencies": [2, 3, 4], "details": "Using a `<style>` tag in the Astro component or a linked stylesheet, define styles for typography (headings, paragraphs), spacing (margins, padding), and layout. Implement media queries to adjust the layout for mobile, tablet, and desktop views, ensuring readability and usability on all devices.", "status": "pending", "testStrategy": "Use browser developer tools to inspect the page at various viewport widths (e.g., 375px, 768px, 1280px). Verify that the layout adapts correctly, text remains readable, and there are no element overflows or visual bugs."}]}, {"id": 9, "title": "Build Resume/CV Section with PDF Download", "description": "Create an interactive online resume page and provide a link to download a PDF version. The page should feature a technical skills matrix with proficiency levels.", "details": "Develop the `src/pages/resume.astro` page. The content can be sourced from an MDX file. Place the resume PDF in the `public/` directory and create a simple `<a>` tag with the `download` attribute pointing to it. For the skills matrix, use a combination of HTML and Tailwind CSS to create a visually clear representation of skills and proficiency levels.", "testStrategy": "Verify that the online resume content is accurate and well-formatted. Test the PDF download link to ensure it works correctly. Check that the skills matrix is easy to understand and visually appealing.", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "Create Astro Page and MDX Content File for Resume", "description": "Set up the basic file structure for the resume page. This includes creating the `src/pages/resume.astro` file and a corresponding MDX file to hold the resume's textual content like work experience and education.", "dependencies": [], "details": "Create the file `src/pages/resume.astro`. Create a new content file, for example `src/content/resume/data.mdx`. In the Astro page, import and render the content from the MDX file to establish the basic page structure and content flow.", "status": "pending", "testStrategy": "Run the development server and navigate to the `/resume` route. Verify that the basic text content from the MDX file is rendered correctly on the page."}, {"id": 2, "title": "Prepare and Add Resume PDF to Public Directory", "description": "Finalize the resume/CV in PDF format and place it within the `public/` directory to make it accessible for download via a static URL.", "dependencies": [], "details": "Create or obtain the final version of the resume as a PDF file. Name it something simple like `resume.pdf`. Move this file into the `public/` folder of the Astro project.", "status": "pending", "testStrategy": "With the dev server running, try to access the PDF directly in the browser by navigating to its URL (e.g., `http://localhost:4321/resume.pdf`). The PDF should display correctly."}, {"id": 3, "title": "Implement the PDF Download Link", "description": "Add a clearly visible link or button on the `resume.astro` page that allows users to download the PDF version of the resume.", "dependencies": [1, 2], "details": "In the `src/pages/resume.astro` file, add an HTML `<a>` tag. Set its `href` attribute to the path of the PDF in the `public` directory (e.g., `/resume.pdf`). Crucially, include the `download` attribute to prompt the browser to download the file. Style the link to be easily noticeable.", "status": "pending", "testStrategy": "Click the download link on the rendered resume page. Confirm that the browser initiates a download of the correct PDF file rather than navigating to it."}, {"id": 4, "title": "Build the Technical Skills Matrix Component", "description": "Create a visually clear skills matrix using HTML and Tailwind CSS to display technical skills and their corresponding proficiency levels.", "dependencies": [1], "details": "Within the `resume.astro` page or a new Astro component, use a `<table>` or a CSS grid of `<div>`s. List technical skills and represent proficiency levels visually (e.g., using filled/unfilled dots, progress bars, or text labels like 'Advanced'). Ensure the matrix is responsive.", "status": "pending", "testStrategy": "Visually inspect the skills matrix on different screen sizes (desktop, tablet, mobile) to ensure it is responsive and readable. Check that the HTML is semantic and accessible using browser developer tools."}, {"id": 5, "title": "Style and Finalize Overall Resume Page Layout", "description": "Apply consistent styling to the entire resume page, integrating the content, download link, and skills matrix into a cohesive and professional layout.", "dependencies": [3, 4], "details": "Use Tailwind CSS utility classes in `src/pages/resume.astro` to style all elements, including headings, paragraphs, and section dividers. Ensure proper margins, padding, and typography for a clean, polished look. Check for visual consistency across all components.", "status": "pending", "testStrategy": "Perform a full visual review of the completed page on multiple modern browsers (Chrome, Firefox, Safari). Use responsive design mode in developer tools to confirm the layout adapts correctly to various viewport sizes."}]}, {"id": 10, "title": "Create Resources Page with Filtering", "description": "Develop the Resources page with a categorized bookmark system. Implement interactive tabs or buttons to filter resources by category (Coding, DevOps, etc.).", "details": "Create `src/pages/resources.astro`. Fetch all entries from the 'resources' content collection. For filtering, use a small amount of client-side JavaScript. Add data attributes to the resource elements (e.g., `data-category='coding'`). Use JavaScript to toggle the visibility of these elements based on which filter button is clicked. This aligns with Astro's island architecture for minimal JS.", "testStrategy": "Verify that all resources are displayed by default. Test the category filtering to ensure it correctly shows and hides resources. Check that the tag system is functional and the layout is responsive.", "priority": "medium", "dependencies": [3, 5], "status": "pending", "subtasks": [{"id": 1, "title": "Create Basic Resources Page and Fetch Data", "description": "Set up the initial `src/pages/resources.astro` file. Fetch all entries from the 'resources' content collection and render them as a simple, unfiltered list to establish the page's foundation.", "dependencies": [], "details": "Create the file `src/pages/resources.astro`. Use Astro's `getCollection('resources')` function to retrieve all resource data. Map over the returned array to render each resource's title, URL, and description within a basic HTML structure (e.g., an unordered list `<ul>`).", "status": "pending", "testStrategy": "Verify that the `/resources` page loads without errors and displays all entries from the 'resources' content collection."}, {"id": 2, "title": "Implement Static Filter UI Controls", "description": "Create the static HTML and CSS for the filter buttons on the `resources.astro` page. Each button should represent a resource category, plus an 'All' option, but will not have any functionality yet.", "dependencies": [1], "details": "In `resources.astro`, add a container for the filter buttons. Manually create a button for 'All' and then dynamically generate a button for each unique category found in the fetched resource data. Assign a `data-filter` attribute to each button corresponding to its category (e.g., `data-filter='coding'`, `data-filter='all'`).", "status": "pending", "testStrategy": "Check the `/resources` page to ensure all category buttons and the 'All' button are rendered correctly with the appropriate `data-filter` attributes in the HTML."}, {"id": 3, "title": "Add Data Attributes to Resource Elements", "description": "Modify the rendering logic in `resources.astro` to include a `data-category` attribute on each resource's container element, which will be used by the JavaScript for filtering.", "dependencies": [1], "details": "In the loop that renders the list of resources, add a `data-category` attribute to the parent HTML element of each resource item (e.g., the `<li>` element). The value of this attribute should be the category slug from the resource's frontmatter (e.g., `data-category={resource.data.category}`).", "status": "pending", "testStrategy": "Inspect the DOM on the `/resources` page to confirm that each rendered resource item has the correct `data-category` attribute matching its content."}, {"id": 4, "title": "Develop Client-Side Filtering Logic", "description": "Write and embed a client-side JavaScript script to handle the filtering functionality. The script will add event listeners to the filter buttons to show or hide resource items based on their `data-category` attribute.", "dependencies": [2, 3], "details": "Add a `<script>` tag within `resources.astro`. Write JavaScript to select all filter buttons and all resource items. Add a click event listener to the buttons. On click, read the button's `data-filter` value. Iterate through all resource items, toggling a 'hidden' class or directly manipulating the `style.display` property based on whether the item's `data-category` matches the filter.", "status": "pending", "testStrategy": "Click on each filter button. Verify that only resources matching the selected category are visible. Clicking 'All' should make all resources visible again."}, {"id": 5, "title": "Style Active Filter State and Transitions", "description": "Enhance the user experience by adding CSS to visually highlight the currently active filter button and apply smooth transitions when items are filtered.", "dependencies": [4], "details": "In the client-side script, add logic to manage an 'active' class on the filter buttons, ensuring only the currently selected one has the class. In your CSS, define styles for this `.active` class (e.g., different background color or border). Also, add CSS `transition` properties to the resource items for effects like opacity or height to make the filtering appear smoother.", "status": "pending", "testStrategy": "Confirm that the clicked filter button receives a distinct visual style. Observe that the showing and hiding of resource items is animated by a CSS transition."}]}, {"id": 11, "title": "Generate RSS Feed for Resources", "description": "Implement an RSS feed for the Resources section to allow users to subscribe to updates. The feed should be auto-generated from the 'resources' content collection.", "details": "Install the official Astro RSS package: `npx astro add rss`. Create an endpoint at `src/pages/resources/rss.xml.js`. In this file, import the `rss` function from `@astrojs/rss` and use `getCollection('resources')` to fetch the data. Map the resource data to the RSS feed item format, including title, link, description, and publication date.", "testStrategy": "Access the `/resources/rss.xml` URL in the browser. Validate the generated XML using a service like the W3C Feed Validation Service. Ensure the content in the feed accurately reflects the content in the MDX files.", "priority": "medium", "dependencies": [10], "status": "pending", "subtasks": [{"id": 1, "title": "Install and Configure Astro RSS Package", "description": "Add the official @astrojs/rss package to the project dependencies to enable RSS feed generation capabilities.", "dependencies": [], "details": "Run the command `npx astro add rss` in the terminal at the project root. Follow the interactive prompts to complete the installation and automatic configuration. This will update `package.json` and may modify `astro.config.mjs`.", "status": "pending", "testStrategy": "Verify that `@astrojs/rss` is present in the `dependencies` section of the `package.json` file. Check `astro.config.mjs` for any changes made by the installer."}, {"id": 2, "title": "Create RSS Feed Endpoint File", "description": "Establish the dedicated API endpoint that will serve the generated RSS feed as an XML file.", "dependencies": [1], "details": "Create a new file named `rss.xml.js` inside the `src/pages/resources/` directory. This file will act as a server-side rendered endpoint for the feed.", "status": "pending", "testStrategy": "Confirm that the file `src/pages/resources/rss.xml.js` exists in the project's file structure. Starting the dev server should not produce any errors related to this new file."}, {"id": 3, "title": "Implement Data Fetching Logic", "description": "Write the code within the endpoint file to fetch all entries from the 'resources' content collection.", "dependencies": [2], "details": "In `src/pages/resources/rss.xml.js`, import `getCollection` from `astro:content`. Create an async `GET` function that calls `await getCollection('resources')` and stores the result in a variable.", "status": "pending", "testStrategy": "Add a `console.log` statement inside the `GET` function to print the fetched collection data. Run the dev server, access the `/resources/rss.xml` endpoint, and check the terminal console to verify that the resource objects are being logged correctly."}, {"id": 4, "title": "Map Resource Data to RSS Item Format", "description": "Transform the array of fetched resource entries into the structure required by the `@astrojs/rss` package for each feed item.", "dependencies": [3], "details": "Using the fetched resources data, apply the `.map()` array method. For each resource item, create a new object containing `title` (from `item.data.title`), `description` (from `item.data.description`), `pubDate` (from `item.data.date`), and a `link` constructed using the resource's slug (e.g., `/resources/${item.slug}/`).", "status": "pending", "testStrategy": "Temporarily modify the endpoint to return the mapped array as a JSON response (`return new Response(JSON.stringify(mappedItems))`). Access the endpoint in a browser and inspect the JSON output to ensure all fields (title, link, description, pubDate) are correctly populated and formatted for every resource."}, {"id": 5, "title": "Generate and Serve the Final RSS Feed", "description": "Use the mapped data and the rss function to generate the final XML output and configure the feed's metadata.", "dependencies": [4], "details": "In `src/pages/resources/rss.xml.js`, import the `rss` function from `@astrojs/rss`. Call the `rss()` function, passing an object with site metadata (e.g., `site`, `title`, `description`) and the `items` property set to your mapped array of resources. Return the result of this function call from your `GET` endpoint.", "status": "pending", "testStrategy": "Run the dev server and navigate to `/resources/rss.xml` in a web browser. Verify that a valid XML document is displayed. Copy the feed URL and use an online RSS feed validator (like the W3C Feed Validation Service) to confirm its structure and content are valid."}]}, {"id": 12, "title": "Implement Contact Form and Social Links", "description": "Integrate a professional contact form with validation. Include direct links for email and LinkedIn, and display location and availability information.", "details": "Create the `src/pages/contact.astro` page. For the form, use a free, serverless form handler like Formspree or Formcarry to avoid managing a backend. The form should have fields for name, email, and message with `required` attributes for basic HTML5 validation. Add `mailto:` and `https://linkedin.com/in/...` links.", "testStrategy": "Submit the form with valid and invalid data to test validation. Send a test message and confirm it is received by the configured email address. Verify that the email and LinkedIn links navigate to the correct destinations.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": [{"id": 1, "title": "Design Contact Form Layout", "description": "Create the HTML structure and Tailwind CSS styling for the contact form component with proper form fields, labels, and responsive design.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 12}, {"id": 2, "title": "Implement Client-Side Form Validation", "description": "Add JavaScript validation for form fields including email format validation, required field checks, and real-time feedback display.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 12}, {"id": 3, "title": "Create Social Links Component", "description": "Build a reusable component for displaying social media links (LinkedIn, GitHub, Email) with proper icons and hover effects.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 12}, {"id": 4, "title": "Implement Contact Page Layout", "description": "Create the contact page structure integrating the contact form, social links, and any additional contact information display areas.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 12}, {"id": 5, "title": "Style and Test Contact Components", "description": "Apply final styling to all contact-related components, ensure responsive design, and test form functionality across different browsers.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 12}]}, {"id": 13, "title": "Implement SEO Best Practices", "description": "Implement all SEO requirements, including automatic sitemap generation, dynamic meta tags, JSON-LD structured data for the professional profile, and canonical URLs.", "details": "Install `@astrojs/sitemap` and configure it in `astro.config.mjs`. In the main `Layout.astro`, dynamically set meta titles, descriptions, and Open Graph tags based on page frontmatter. Create a component to generate JSON-LD for the `Person` schema type on the homepage and `Article` on portfolio pages. Ensure `robots.txt` is configured correctly in the `public/` directory.", "testStrategy": "Build the site and inspect the generated `sitemap.xml`. Use browser developer tools to check for meta tags and JSON-LD data on various pages. Use an online tool like Google's Rich Results Test to validate the structured data.", "priority": "high", "dependencies": [4, 7, 8, 9, 10], "status": "pending", "subtasks": [{"id": 1, "title": "Configure Automatic Sitemap Generation", "description": "Install the `@astrojs/sitemap` integration and configure it in `astro.config.mjs` to automatically generate a `sitemap-index.xml` file during the build process.", "dependencies": [], "details": "Run `npx astro add sitemap` or manually install `@astrojs/sitemap` and add it to the `integrations` array in the `astro.config.mjs` file. Ensure the `site` property is correctly set in the config.", "status": "pending", "testStrategy": "After running `npm run build`, verify that a `dist/sitemap-index.xml` file is created. Inspect its contents to ensure it correctly lists all public pages of the website."}, {"id": 2, "title": "Implement Dynamic Meta and Open Graph Tags", "description": "Update the main `Layout.astro` component to dynamically generate `<title>`, `<meta name=\"description\">`, and Open Graph tags (e.g., `og:title`, `og:description`, `og:image`) based on the frontmatter of each page.", "dependencies": [], "details": "In `src/layouts/Layout.astro`, use `Astro.props` to access frontmatter variables like `title` and `description`. Set sensible default values for any pages that might be missing this frontmatter. The title format should be 'Page Title | Site Name'.", "status": "pending", "testStrategy": "Inspect the HTML `<head>` on the homepage, a portfolio page, and the about page using browser developer tools. Confirm that the title, description, and OG tags are unique and accurately reflect each page's content."}, {"id": 3, "title": "Create JSON-LD Component for 'Person' Schema", "description": "Develop a new Astro component that generates a JSON-LD `<script>` tag for the `Person` schema type. This component should be used on the homepage to describe the professional profile.", "dependencies": [], "details": "Create a component, e.g., `src/components/JsonLdPerson.astro`, that outputs a `<script type=\"application/ld+json\">`. It should be populated with static data representing the professional profile (name, url, sameAs for social profiles). Add this component to the `<head>` of the homepage.", "status": "pending", "testStrategy": "Deploy the homepage to a preview environment and use the Google Rich Results Test tool to validate the structured data. Ensure there are no errors or warnings for the `Person` schema."}, {"id": 4, "title": "Implement JSON-LD for 'Article' Schema on Portfolio Pages", "description": "Create or extend a component to generate JSON-LD structured data for the `Article` schema type on individual portfolio project pages, pulling data from page frontmatter.", "dependencies": [2], "details": "Create a component, e.g., `src/components/JsonLdArticle.astro`, that accepts props from the portfolio page's frontmatter (`title`, `publishDate`, `description`, `image`). This component will generate the `Article` schema script and should be included in the layout for portfolio detail pages.", "status": "pending", "testStrategy": "Use the Google Rich Results Test tool on several live or previewed portfolio pages. Verify that the `Article` structured data is valid and that dynamic fields like `headline` and `datePublished` are correctly populated from the frontmatter."}, {"id": 5, "title": "Configure Canonical URLs and robots.txt", "description": "Ensure every page has a canonical link tag pointing to its definitive URL and create a `robots.txt` file to guide search engine crawlers and point them to the sitemap.", "dependencies": [1], "details": "In the main `Layout.astro`, add a `<link rel=\"canonical\" href={Astro.url.href} />` tag inside the `<head>`. Create a `robots.txt` file in the `public/` directory. The file should include `User-agent: *`, `Allow: /`, and `Sitemap: https://<your-domain>/sitemap-index.xml`.", "status": "pending", "testStrategy": "Inspect the generated HTML of several different pages to confirm the `rel=\"canonical\"` link tag points to the correct, absolute URL. After deployment, verify that `https://<your-domain>/robots.txt` is accessible and contains the correct sitemap URL."}]}, {"id": 14, "title": "Accessibility and Performance Optimization", "description": "Perform a comprehensive audit to ensure the website meets all quality gates, including a Lighthouse score of 95+, WCAG 2.1 AA compliance, and cross-browser compatibility.", "details": "Use Astro's built-in image optimization via `<Image />` or `<Picture />` components for all images. Minify CSS and JS (handled automatically by `astro build`). Run Lighthouse in Chrome DevTools on all key pages. Use the `axe` browser extension to identify and fix accessibility issues like contrast ratios, alt text, and keyboard navigation. Test on latest versions of Chrome, Firefox, and Safari.", "testStrategy": "Document Lighthouse scores for each page, ensuring they are above 95. Create a checklist for WCAG 2.1 AA requirements and verify each one. Manually test keyboard navigation and screen reader functionality (e.g., using VoiceOver or NVDA).", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": [{"id": 1, "title": "Implement Astro Image Optimization", "description": "Refactor all `<img>` tags across the website to use Astro's built-in `<Image />` or `<Picture />` components to leverage automatic optimization, including format conversion, resizing, and lazy loading.", "dependencies": [], "details": "Audit all pages and components to locate static image assets. Replace each standard `<img>` tag with the Astro `<Image>` component. For images requiring art direction or multiple formats, use the `<Picture>` component. Ensure all images have appropriate `alt` text for accessibility.", "status": "pending", "testStrategy": "Visually inspect all pages to confirm images render correctly. Use browser developer tools (Network tab) to verify that images are served in modern formats (e.g., WebP) and are appropriately sized for their viewport containers."}, {"id": 2, "title": "Conduct WCAG 2.1 AA Accessibility Audit and Remediation", "description": "Use the `axe DevTools` browser extension to perform a comprehensive accessibility audit on all key pages. Identify and remediate all reported issues to ensure compliance with WCAG 2.1 Level AA standards.", "dependencies": [], "details": "Install and run the `axe` extension on the homepage, key landing pages, and pages with complex components (forms, modals). Systematically fix issues related to color contrast, ARIA roles, alt text, form labels, and heading structure. Manually test keyboard navigation to ensure all interactive elements are focusable and usable.", "status": "pending", "testStrategy": "After implementing fixes, re-run the `axe` scan on each page to confirm a '0 issues' result. Manually tab through the entire site to verify a logical focus order and visible focus indicators on all interactive elements."}, {"id": 3, "title": "Run Initial Lighthouse Performance Audit", "description": "Establish a performance baseline by running a Lighthouse audit on all key pages. Analyze the report to identify the most significant opportunities for improvement beyond initial image and asset optimization.", "dependencies": [1], "details": "Using an incognito window in Chrome DevTools, run a Lighthouse audit (focused on Performance) for the homepage, a product/service page, and a content-heavy page (like a blog post). Document the initial scores and pay close attention to Core Web Vitals (LCP, INP, CLS) and other key metrics like FCP and TBT.", "status": "pending", "testStrategy": "The Lighthouse report itself will serve as the test result. The goal is to identify specific, actionable items listed in the 'Opportunities' and 'Diagnostics' sections of the report to guide further optimization efforts."}, {"id": 4, "title": "Perform Cross-Browser Compatibility Testing", "description": "Manually test the website's rendering and functionality on the latest stable versions of Google Chrome, Mozilla Firefox, and Apple Safari to ensure a consistent user experience.", "dependencies": [1, 2], "details": "Create a checklist of key user flows (e.g., navigation, form submission, interactive elements). Execute this checklist on each target browser, documenting any visual discrepancies, layout breaks, or functional errors in a bug tracking system or shared document.", "status": "pending", "testStrategy": "The test passes when all items on the checklist are successfully completed in Chrome, Firefox, and Safari with no significant visual or functional regressions. Screenshots should be used to document any identified issues."}, {"id": 5, "title": "Final Verification and Quality Gate Sign-off", "description": "Conduct a final, comprehensive audit to confirm all quality gates have been met. This includes achieving a Lighthouse performance score of 95+, passing the WCAG 2.1 AA `axe` scan, and verifying cross-browser compatibility.", "dependencies": [3, 4], "details": "After all remediation work is complete, run a final Lighthouse audit on the key pages to confirm the 95+ score. Run a final `axe` scan to ensure no new accessibility issues were introduced. Give the site a final walkthrough on all three target browsers.", "status": "pending", "testStrategy": "The test is successful when a final Lighthouse report shows a performance score of 95 or higher, the `axe` scan reports zero critical or serious issues, and the final cross-browser check reveals no regressions. Archive these final reports as proof of completion."}]}, {"id": 15, "title": "Deploy to Vercel and Go Live", "description": "Deploy the final website to Vercel. Connect the custom domain, enforce HTTPS, and perform a final end-to-end review of the live site.", "details": "In the Vercel dashboard, import the GitHub repository. Vercel should automatically detect it as an Astro project. Configure the build command (`npm run build`) and output directory (`dist`). Add the custom domain and let Vercel provision the SSL certificate. Set up environment variables if any are needed. After deployment, perform a full site walkthrough.", "testStrategy": "Verify that the deployment completes successfully. Access the site via the custom domain and ensure HTTPS is enforced. Perform a final check of all pages, links, forms, and responsive layouts on the live production environment.", "priority": "high", "dependencies": [14], "status": "pending", "subtasks": [{"id": 1, "title": "Create Vercel Project and Connect GitHub Repository", "description": "Initialize a new project within the Vercel dashboard and link it to the appropriate GitHub repository containing the Astro project source code.", "dependencies": [], "details": "Log in to the Vercel dashboard. Use the 'Add New... > Project' flow. Select the correct GitHub repository from the list. <PERSON> Ver<PERSON> the necessary permissions to access the repository if prompted. This establishes the connection for continuous deployment.", "status": "pending"}, {"id": 2, "title": "Configure Build Settings and Environment Variables", "description": "Set up the project's build command, output directory, and add any required environment variables for the production environment.", "dependencies": [1], "details": "In the Vercel project settings, navigate to the 'General' section. Verify that Vercel has correctly identified the framework as Astro. Confirm the 'Build Command' is set to `npm run build` and the 'Output Directory' is `dist`. Navigate to the 'Environment Variables' section and add all necessary production keys and values.", "status": "pending"}, {"id": 3, "title": "Trigger Initial Deployment and Verify on Vercel Domain", "description": "Run the first deployment to a Vercel-generated domain and perform a quick verification to ensure the site builds and functions correctly before attaching a custom domain.", "dependencies": [2], "details": "After configuring the settings, a deployment should trigger automatically. If not, manually trigger one from the 'Deployments' tab. Once the deployment status is 'Ready', access the site using the provided Vercel domain (e.g., `my-project.vercel.app`). Perform a smoke test to ensure the homepage and key pages load without errors.", "status": "pending", "testStrategy": "Access the Vercel-provided URL. Check that the main page loads correctly. Click on 2-3 primary navigation links to ensure routing works. Open the browser's developer console to check for any critical errors."}, {"id": 4, "title": "Connect Custom Domain and Verify SSL", "description": "Add the custom domain to the Vercel project, configure the necessary DNS records at the domain registrar, and confirm that Vercel has successfully provisioned the SSL certificate.", "dependencies": [3], "details": "In the Vercel project settings, go to the 'Domains' tab. Add the custom domain (e.g., `www.example.com` and `example.com`). Vercel will provide the required DNS records (e.g., A record or CNAME). Update these records in your domain registrar's DNS management panel. Wait for DNS propagation, after which Vercel will automatically issue and install an SSL certificate.", "status": "pending"}, {"id": 5, "title": "Perform Final End-to-End Review on Live Domain", "description": "Conduct a comprehensive review of the entire website on the live custom domain to ensure all pages, links, and functionalities are working as expected and that HTTPS is enforced.", "dependencies": [4], "details": "Systematically browse the entire live site using the custom domain. Check all pages, test all forms, and verify that all internal and external links work. Confirm that all images and assets load correctly. Test the site's responsiveness on various screen sizes. Attempt to access the site via HTTP to ensure it automatically redirects to HTTPS.", "status": "pending", "testStrategy": "Create a test plan covering all site pages and key user flows (e.g., form submission, navigation). Execute the plan on desktop and mobile viewports. Use a broken link checker tool for a final sweep. Verify the SSL certificate is valid in the browser."}, {"id": 6, "title": "Implement Google Analytics Integration", "description": "Install and configure Google Analytics 4 tracking code, implement page view tracking, and set up basic event tracking for the website.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 15}]}], "metadata": {"created": "2025-06-21T07:37:50.035Z", "updated": "2025-06-21T07:37:50.035Z", "description": "Tasks for master context"}}}